import React from 'react';
import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  format,
  isSameMonth,
  isToday,
} from 'date-fns';
import { CalendarSyncIndicator } from '../../tasks/CalendarSyncIndicator';
import { TaskModel } from '../../../../lib/types/task.model';

interface MonthViewProps {
  selectedDate: Date;
  filters: {
    categories: string[];
    priority: string;
    status: string;
  };
}

const MonthView: React.FC<MonthViewProps> = ({ selectedDate, filters }) => {
  const monthStart = startOfMonth(selectedDate);
  const monthEnd = endOfMonth(selectedDate);
  const calendarStart = startOfWeek(monthStart);
  const calendarEnd = endOfWeek(monthEnd);

  const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Mock tasks - replace with actual task fetching
  const tasks: TaskModel[] = [];

  const getTasksForDate = (date: Date) => {
    return tasks.filter(task => {
      if (!task.deadline) return false;
      const taskDate = new Date(task.deadline);
      return (
        taskDate.getDate() === date.getDate() &&
        taskDate.getMonth() === date.getMonth() &&
        taskDate.getFullYear() === date.getFullYear()
      );
    });
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Week day headers */}
      <div className="grid grid-cols-7 gap-px bg-gray-200">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div
            key={day}
            className="py-2 text-center text-sm font-semibold text-gray-700"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-px bg-gray-200">
        {days.map((day, dayIdx) => {
          const dayTasks = getTasksForDate(day);
          const isCurrentMonth = isSameMonth(day, selectedDate);

          return (
            <div
              key={day.toString()}
              className={`min-h-[120px] bg-white p-2 ${
                !isCurrentMonth ? 'bg-gray-50' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <span
                  className={`text-sm ${
                    !isCurrentMonth
                      ? 'text-gray-400'
                      : isToday(day)
                      ? 'bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center'
                      : 'text-gray-700'
                  }`}
                >
                  {format(day, 'd')}
                </span>
                {dayTasks.length > 0 && (
                  <span className="text-xs text-gray-500">
                    {dayTasks.length} tasks
                  </span>
                )}
              </div>

              {/* Tasks for the day */}
              <div className="mt-1 space-y-1">
                {dayTasks.slice(0, 3).map(task => (
                  <div
                    key={task._id}
                    className="text-xs p-1 rounded bg-blue-50 text-blue-700 flex items-center justify-between"
                  >
                    <span className="truncate">{task.title}</span>
                    <CalendarSyncIndicator
                      syncStatus={task.calendarSyncStatus === 'not_synced' ? undefined : task.calendarSyncStatus}
                      lastSync={task.calendarLastSyncedAt ? new Date(task.calendarLastSyncedAt) : null}
                    />
                  </div>
                ))}
                {dayTasks.length > 3 && (
                  <div className="text-xs text-gray-500 pl-1">
                    +{dayTasks.length - 3} more
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MonthView;
