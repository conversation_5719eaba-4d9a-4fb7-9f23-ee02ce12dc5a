import React, { useState } from 'react';
import { format } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from 'lucide-react';
import MonthView from './views/MonthView';
import WeekView from './views/WeekView';
import DayView from './views/DayView';
// import MiniCalendar from './MiniCalendar'; // Temporarily disabled due to issues
import FilterPanel from './FilterPanel';
import UpcomingTasks from './UpcomingTasks';

type ViewType = 'month' | 'week' | 'day';

const CalendarPage: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [view, setView] = useState<ViewType>('month');
  const [filters, setFilters] = useState({
    categories: [] as string[],
    priority: 'all',
    status: 'all'
  });

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    if (view === 'month') {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    }
    setSelectedDate(newDate);
  };

  const renderViewComponent = () => {
    switch (view) {
      case 'month':
        return <MonthView selectedDate={selectedDate} filters={filters} />;
      case 'week':
        return <WeekView selectedDate={selectedDate} filters={filters} />;
      case 'day':
        return <DayView selectedDate={selectedDate} filters={filters} />;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Left Sidebar */}
      <div className="w-1/4 bg-white p-4 border-r border-gray-200">
        {/* MiniCalendar temporarily disabled due to issues
        <MiniCalendar
          selectedDate={selectedDate}
          onDateSelect={setSelectedDate}
        />
        */}
        <FilterPanel
          filters={filters}
          onFiltersChange={setFilters}
        />
        <UpcomingTasks
          selectedDate={selectedDate}
          filters={filters}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white p-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigateDate('prev')}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h1 className="text-xl font-semibold">
              {format(selectedDate, 'MMMM yyyy')}
            </h1>
            <button
              onClick={() => navigateDate('next')}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setSelectedDate(new Date())}
              className="px-4 py-2 text-sm bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100"
            >
              Today
            </button>
            <div className="border-l border-gray-300 h-6 mx-2" />
            <div className="flex bg-gray-100 rounded-md">
              {(['month', 'week', 'day'] as ViewType[]).map((viewType) => (
                <button
                  key={viewType}
                  onClick={() => setView(viewType)}
                  className={`px-4 py-2 text-sm ${
                    view === viewType
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {viewType.charAt(0).toUpperCase() + viewType.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Calendar View */}
        <div className="flex-1 overflow-auto p-4">
          {renderViewComponent()}
        </div>
      </div>
    </div>
  );
};

export default CalendarPage;
