"use client";

import React, { createContext, useState, useContext, useCallback, useEffect, ReactNode, useMemo } from 'react';
import {
  groceryService,
  GroceryItem
} from '@/lib/grocery-service';
import {
  GroceryList,
  GroceryCollaborator,
  GroceryShareSettings,
  GroceryListInvitation
} from '@/lib/types/grocery.model';
import { useToast } from '@/hooks/use-toast';
import { GROCERY_CATEGORIES } from '@/lib/constants';
import { useUserPreferences } from './user-preferences-context';

// Define the shape for insights data
interface GroceryInsights {
  lastShoppingTrip: Date | null;
  mostFrequentItem: string | null;
  topCategory: { name: string; percentage: number } | null;
}

// Define the shape of the context data
interface GroceryContextType {
  // Existing grocery items state
  items: GroceryItem[];
  isLoading: boolean;
  error: string | null;
  suggestions: string[];
  isLoadingSuggestions: boolean;
  errorSuggestions: string | null;
  insights: GroceryInsights | null;
  isLoadingInsights: boolean;
  errorInsights: string | null;

  // NEW: Collaboration state
  groceryList: GroceryList | null;
  isShared: boolean;
  collaborators: GroceryCollaborator[];
  shareSettings: GroceryShareSettings | null;
  userRole: 'owner' | 'editor' | 'viewer' | null;
  sharedLists: GroceryList[];
  pendingInvitations: GroceryListInvitation[];
  isLoadingCollaboration: boolean;
  collaborationError: string | null;
  currentListOwnerId: string | null;

  // Existing grocery methods
  fetchGroceries: (listOwnerId?: string) => Promise<void>;
  addGroceryItems: (itemsToAdd: string[] | {name: string, quantity?: string}[]) => Promise<void>;
  updateGroceryItem: (id: string, updates: Partial<GroceryItem>) => Promise<void>;
  deleteGroceryItem: (id: string) => Promise<void>;
  fetchSuggestions: () => Promise<void>;
  fetchInsights: () => Promise<void>;
  updateCategories: () => Promise<{updated: number; total: number}>;
  triggerRefresh: () => void;
  refreshGroceries: () => void;

  // NEW: Collaboration methods
  fetchGroceryList: () => Promise<void>;
  enableSharing: (shareSettings: GroceryShareSettings) => Promise<void>;
  disableSharing: () => Promise<void>;
  updateShareSettings: (shareSettings: GroceryShareSettings) => Promise<void>;
  addCollaborator: (email: string, role: 'editor' | 'viewer') => Promise<void>;
  removeCollaborator: (collaboratorId: string) => Promise<void>;
  fetchSharedLists: () => Promise<void>;
  fetchPendingInvitations: () => Promise<void>;
  acceptInvitation: (token: string) => Promise<void>;
  declineInvitation: (token: string) => Promise<void>;
  switchToSharedList: (listOwnerId: string) => Promise<void>;
  switchToMyList: () => Promise<void>;
}

// Create the context with a default value
const GroceryContext = createContext<GroceryContextType | undefined>(undefined);

// Define the props for the provider component
interface GroceryProviderProps {
  children: ReactNode;
}

// Create the provider component
export const GroceryProvider: React.FC<GroceryProviderProps> = ({ children }) => {
  // State for grocery items
  const [items, setItems] = useState<GroceryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for suggestions
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [errorSuggestions, setErrorSuggestions] = useState<string | null>(null);

  // State for insights
  const [insights, setInsights] = useState<GroceryInsights | null>(null);
  const [isLoadingInsights, setIsLoadingInsights] = useState(true);
  const [errorInsights, setErrorInsights] = useState<string | null>(null);

  // NEW: State for collaboration
  const [groceryList, setGroceryList] = useState<GroceryList | null>(null);
  const [sharedLists, setSharedLists] = useState<GroceryList[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<GroceryListInvitation[]>([]);
  const [isLoadingCollaboration, setIsLoadingCollaboration] = useState(false);
  const [collaborationError, setCollaborationError] = useState<string | null>(null);
  const [currentListOwnerId, setCurrentListOwnerId] = useState<string | null>(null); // Track which list we're viewing

  const [refreshCounter, setRefreshCounter] = useState(0); // State to trigger refreshes
  const { toast } = useToast();
  const { groceryPreferences } = useUserPreferences();

  // Derived collaboration state
  const isShared = groceryList?.isShared || false;
  const collaborators = groceryList?.collaborators || [];
  const shareSettings = groceryList?.shareSettings || null;
  const userRole = groceryList?.userRole || null;

  const triggerRefresh = useCallback(() => {
    setRefreshCounter(prev => prev + 1);
  }, []);

  // Function to fetch grocery items (supports shared lists)
  const fetchGroceries = useCallback(async (listOwnerId?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedItems = await groceryService.getGroceryItems(listOwnerId);
      setItems(fetchedItems);
      setCurrentListOwnerId(listOwnerId || null);
    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch grocery items:", err);
      const errorMessage = err.message || 'Failed to load groceries.';
      setError(errorMessage);
      toast({
        title: 'Error Loading Groceries',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Function to fetch suggestions
  const fetchSuggestions = useCallback(async () => {
    setIsLoadingSuggestions(true);
    setErrorSuggestions(null);
    try {
      // Fetch more initially to allow filtering based on current items
      // Pass current list context to get suggestions from the appropriate library
      const fetchedSuggestions = await groceryService.getGrocerySuggestions(10, currentListOwnerId);

      // Filter out suggestions already present in the *unchecked* items list
      const currentUncheckedItemNamesLower = items
        .filter(item => !item.isChecked)
        .map(item => item.name.toLowerCase());

      const uniqueSuggestions = fetchedSuggestions.filter(sugg => !currentUncheckedItemNamesLower.includes(sugg.toLowerCase()));

      // Use defaults if API returns none or errors occur
      if (uniqueSuggestions.length === 0) {
        const defaultSuggestions = ["milk", "bread", "eggs", "cheese", "apples", "bananas"];
        const filteredDefaults = defaultSuggestions.filter(
          sugg => !currentUncheckedItemNamesLower.includes(sugg.toLowerCase())
        );
        setSuggestions(filteredDefaults.slice(0, 5)); // Limit defaults
      } else {
        setSuggestions(uniqueSuggestions); // Store filtered suggestions
      }

    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch grocery suggestions:", err);
      setErrorSuggestions(err.message || 'Could not load suggestions.');
      // Fallback to defaults on error
      const defaultSuggestions = ["milk", "bread", "eggs", "cheese", "apples"];
      const currentUncheckedItemNamesLower = items
        .filter(item => !item.isChecked)
        .map(item => item.name.toLowerCase());
      const filteredDefaults = defaultSuggestions.filter(
        sugg => !currentUncheckedItemNamesLower.includes(sugg.toLowerCase())
      );
      setSuggestions(filteredDefaults.slice(0, 5));
    } finally {
      setIsLoadingSuggestions(false);
    }
  }, [items, currentListOwnerId]); // Re-fetch suggestions when items or current list context changes

  // Function to fetch insights
  const fetchInsights = useCallback(async () => {
    setIsLoadingInsights(true);
    setErrorInsights(null);
    try {
      const fetchedInsights = await groceryService.getGroceryInsights();
      setInsights(fetchedInsights);
    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch insights:", err);
      const errorMessage = err.message || 'Failed to load shopping insights.';
      setErrorInsights(errorMessage);
      // Optional: Toast for insights error?
    } finally {
      setIsLoadingInsights(false);
    }
  }, []); // No dependencies needed for now

  // Function to update a grocery item
  const updateGroceryItem = useCallback(async (id: string, updates: Partial<GroceryItem>) => {
    // Optimistic update (optional but good for UX)
    const originalItems = [...items];

    // Ensure we preserve the category color if it's not explicitly provided in the updates
    const updatedItems = (prevItems: GroceryItem[]) =>
      prevItems.map(item => {
        if (item._id === id) {
          // If updates include category name but not color, preserve the existing color
          let updatedItem = { ...item, ...updates };

          // Special handling for category to preserve color
          if (updates.category && updates.category.name && !updates.category.color) {
            // Find the matching category from constants or use the existing color
            const existingColor = item.category?.color;
            const matchingCategory = GROCERY_CATEGORIES.find(cat => cat.name === updates.category?.name);

            updatedItem.category = {
              name: updates.category.name,
              color: matchingCategory?.color || existingColor || 'bg-gray-500'
            };
          }

          return updatedItem;
        }
        return item;
      });

    // Apply the optimistic update
    setItems(updatedItems);

    try {
      // Ensure we're sending the category with color to the backend
      let updatesToSend = { ...updates };

      // If updates include category name but not color, add the color
      if (updates.category && updates.category.name && !updates.category.color) {
        // Find the item we're updating to get its current category color
        const itemToUpdate = items.find(item => item._id === id);
        const existingColor = itemToUpdate?.category?.color;
        const matchingCategory = GROCERY_CATEGORIES.find(cat => cat.name === updates.category?.name);

        updatesToSend.category = {
          name: updates.category.name,
          color: matchingCategory?.color || existingColor || 'bg-gray-500'
        };
      }

      // Send the updated data to the backend
      const updatedItem = await groceryService.updateGroceryItem(id, updatesToSend);

      // Update the item in state with the response from the backend
      setItems(prevItems =>
        prevItems.map(item =>
          item._id === id ? updatedItem : item
        )
      );
    } catch (err: any) {
      console.error("GroceryContext: Failed to update grocery item:", err);
      const errorMessage = err.message || 'Could not update item.';
      setError(errorMessage); // Set error state
      toast({
        title: 'Update Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      // Revert optimistic update on failure
      setItems(originalItems);
      throw err; // Re-throw
    }
  }, [items, toast]);

  // Function to add grocery items (supports shared lists)
  const addGroceryItems = useCallback(async (itemsToAdd: string[] | {name: string, quantity?: string}[]) => {
    if (itemsToAdd.length === 0) return;

    // Convert input to the expected format
    const formattedItems = itemsToAdd.map(item => {
      if (typeof item === 'string') {
        // Parse string for potential quantity pattern like "2 apples" or "1kg rice"
        const match = item.match(/^([\d.]+)\s+(.+)$/);
        if (match) {
          return {
            name: match[2].trim(),
            quantity: match[1].trim()
          };
        }
        return { name: item };
      }
      return item; // Already in correct format
    });

    let itemsAdded = 0;
    let itemsUpdated = 0;
    try {
      for (const newItem of formattedItems) {
        // Find existing item (case-insensitive, unchecked only)
        const existing = items.find(
          item => item.name.trim().toLowerCase() === newItem.name.trim().toLowerCase() && !item.isChecked
        );
        if (existing) {
          // Merge quantities if both are numeric
          let mergedQuantity = newItem.quantity || existing.quantity;
          const newNum = parseFloat(newItem.quantity || '');
          const existNum = parseFloat(existing.quantity || '');
          if (!isNaN(newNum) && !isNaN(existNum)) {
            mergedQuantity = String(newNum + existNum);
          } else if (newItem.quantity && existing.quantity && newItem.quantity !== existing.quantity) {
            // If both exist but not numeric, concatenate
            mergedQuantity = `${existing.quantity}, ${newItem.quantity}`;
          }
          await updateGroceryItem(existing._id, { quantity: mergedQuantity });
          itemsUpdated++;
        } else {
          // Add as new - pass currentListOwnerId for shared lists
          await groceryService.addGroceryItems({ items: [newItem] }, currentListOwnerId);
          itemsAdded++;
        }
      }
      const summary = [
        itemsAdded > 0 ? `${itemsAdded} new item(s) added` : '',
        itemsUpdated > 0 ? `${itemsUpdated} item(s) updated` : ''
      ].filter(Boolean).join(', ');
      toast({
        title: 'Groceries Updated',
        description: summary || 'No changes made.',
      });
      triggerRefresh();
    } catch (err: any) {
      console.error("GroceryContext: Failed to add/update grocery items:", err);
      const errorMessage = err.message || 'Could not add/update items.';
      setError(errorMessage);
      toast({
        title: 'Add/Update Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast, triggerRefresh, items, updateGroceryItem, currentListOwnerId]);



  // Function to delete a grocery item
  const deleteGroceryItem = useCallback(async (id: string) => {
    const originalItems = [...items];
    // Optimistic update
    setItems(prevItems => prevItems.filter(item => item._id !== id));

    try {
      await groceryService.deleteGroceryItem(id);
      toast({
        title: 'Item Deleted',
        description: 'Grocery item removed successfully.',
      });
      // No need to trigger full refresh if optimistic update is sufficient
    } catch (err: any) {
      console.error("GroceryContext: Failed to delete grocery item:", err);
      const errorMessage = err.message || 'Could not delete item.';
      setError(errorMessage); // Set error state
      toast({
        title: 'Delete Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      // Revert optimistic update
      setItems(originalItems);
      throw err; // Re-throw
    }
  }, [items, toast]); // Include items if optimistic update relies on it

  // Function to update categories for uncategorized items
  const updateCategories = useCallback(async () => {
    try {
      const result = await groceryService.updateCategories();

      // Show a toast notification
      if (result.updated > 0) {
        toast({
          title: "Categories updated",
          description: `Updated categories for ${result.updated} of ${result.total} items.`,
          variant: "default",
        });
      } else {
        toast({
          title: "No updates needed",
          description: "All items already have categories assigned.",
          variant: "default",
        });
      }

      // Refresh the grocery list
      await fetchGroceries(currentListOwnerId || undefined);

      return result;
    } catch (error: any) {
      console.error("Failed to update categories:", error);
      toast({
        title: "Failed to update categories",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
      throw error;
    }
  }, [fetchGroceries, toast, currentListOwnerId]);

  // ===== COLLABORATION METHODS =====

  // Function to fetch grocery list metadata
  const fetchGroceryList = useCallback(async () => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const list = await groceryService.getGroceryList();
      setGroceryList(list);
    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch grocery list metadata:", err);
      const errorMessage = err.message || 'Failed to load list information.';
      setCollaborationError(errorMessage);
      // Don't show toast for this as it's background data
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, []);

  // Function to enable sharing
  const enableSharing = useCallback(async (shareSettings: GroceryShareSettings) => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const updatedList = await groceryService.enableSharing(shareSettings);
      setGroceryList(updatedList);
      toast({
        title: 'Sharing Enabled',
        description: 'Your grocery list is now shared and ready for collaboration.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to enable sharing:", err);
      const errorMessage = err.message || 'Failed to enable sharing.';
      setCollaborationError(errorMessage);
      toast({
        title: 'Failed to Enable Sharing',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, [toast]);

  // Function to disable sharing
  const disableSharing = useCallback(async () => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const updatedList = await groceryService.disableSharing();
      setGroceryList(updatedList);
      toast({
        title: 'Sharing Disabled',
        description: 'Your grocery list is no longer shared.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to disable sharing:", err);
      const errorMessage = err.message || 'Failed to disable sharing.';
      setCollaborationError(errorMessage);
      toast({
        title: 'Failed to Disable Sharing',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, [toast]);

  // Function to update share settings
  const updateShareSettings = useCallback(async (shareSettings: GroceryShareSettings) => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const updatedList = await groceryService.updateShareSettings(shareSettings);
      setGroceryList(updatedList);
      toast({
        title: 'Settings Updated',
        description: 'Share settings have been updated successfully.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to update share settings:", err);
      const errorMessage = err.message || 'Failed to update settings.';
      setCollaborationError(errorMessage);
      toast({
        title: 'Failed to Update Settings',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, [toast]);

  // Function to add collaborator
  const addCollaborator = useCallback(async (email: string, role: 'editor' | 'viewer') => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const result = await groceryService.addCollaborator(email, role);
      setGroceryList(result.list);
      toast({
        title: 'Collaborator Added',
        description: `${email} has been added to your grocery list.`,
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to add collaborator:", err);
      const errorMessage = err.message || 'Failed to add collaborator.';
      setCollaborationError(errorMessage);
      toast({
        title: 'Failed to Add Collaborator',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, [toast]);

  // Function to remove collaborator
  const removeCollaborator = useCallback(async (collaboratorId: string) => {
    setIsLoadingCollaboration(true);
    setCollaborationError(null);
    try {
      const updatedList = await groceryService.removeCollaborator(collaboratorId);
      setGroceryList(updatedList);
      toast({
        title: 'Collaborator Removed',
        description: 'The collaborator has been removed from your grocery list.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to remove collaborator:", err);
      const errorMessage = err.message || 'Failed to remove collaborator.';
      setCollaborationError(errorMessage);
      toast({
        title: 'Failed to Remove Collaborator',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoadingCollaboration(false);
    }
  }, [toast]);

  // Function to fetch shared lists
  const fetchSharedLists = useCallback(async () => {
    try {
      const lists = await groceryService.getSharedLists();
      setSharedLists(lists);
    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch shared lists:", err);
      // Don't show toast for background data
    }
  }, []);

  // Function to fetch pending invitations
  const fetchPendingInvitations = useCallback(async () => {
    try {
      const invitations = await groceryService.getPendingInvitations();
      setPendingInvitations(invitations);
    } catch (err: any) {
      console.error("GroceryContext: Failed to fetch pending invitations:", err);
      // Don't show toast for background data
    }
  }, []);

  // Function to accept invitation
  const acceptInvitation = useCallback(async (token: string) => {
    try {
      await groceryService.acceptInvitation(token);
      await fetchSharedLists(); // Refresh shared lists
      await fetchPendingInvitations(); // Refresh pending invitations
      toast({
        title: 'Invitation Accepted',
        description: 'You have successfully joined the shared grocery list.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to accept invitation:", err);
      const errorMessage = err.message || 'Failed to accept invitation.';
      toast({
        title: 'Failed to Accept Invitation',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [fetchSharedLists, fetchPendingInvitations, toast]);

  // Function to decline invitation
  const declineInvitation = useCallback(async (token: string) => {
    try {
      await groceryService.declineInvitation(token);
      await fetchPendingInvitations(); // Refresh pending invitations
      toast({
        title: 'Invitation Declined',
        description: 'You have declined the invitation.',
      });
    } catch (err: any) {
      console.error("GroceryContext: Failed to decline invitation:", err);
      const errorMessage = err.message || 'Failed to decline invitation.';
      toast({
        title: 'Failed to Decline Invitation',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [fetchPendingInvitations, toast]);

  // Function to switch to a shared list
  const switchToSharedList = useCallback(async (listOwnerId: string) => {
    await fetchGroceries(listOwnerId);
  }, [fetchGroceries]);

  // Function to switch back to user's own list
  const switchToMyList = useCallback(async () => {
    await fetchGroceries();
  }, [fetchGroceries]);

  // Fetch initial data and suggestions on mount and when refreshCounter changes
  useEffect(() => {
    fetchGroceries(currentListOwnerId || undefined); // Respect current list context
    fetchInsights(); // Fetch insights on initial load/refresh
    fetchGroceryList(); // Fetch list metadata for collaboration features
    fetchSharedLists(); // Fetch shared lists
    fetchPendingInvitations(); // Fetch pending invitations
  }, [fetchGroceries, fetchInsights, fetchGroceryList, fetchSharedLists, fetchPendingInvitations, refreshCounter, currentListOwnerId]);

  // Fetch suggestions after initial items load or when items change
   useEffect(() => {
     if (!isLoading) { // Only fetch suggestions after initial item load
       fetchSuggestions();
     }
   }, [isLoading, fetchSuggestions]); // Depend on isLoading and fetchSuggestions

  // Auto-switch to default list based on user preferences
  useEffect(() => {
    if (groceryPreferences?.autoSwitchToDefault &&
        groceryPreferences.defaultListType === 'shared' &&
        groceryPreferences.defaultSharedListOwnerId &&
        !currentListOwnerId && // Only auto-switch if not already on a specific list
        sharedLists.length > 0) {

      // Check if the default shared list is available
      const defaultSharedList = sharedLists.find(list => {
        const userId = typeof list.userId === 'object' ? list.userId._id : list.userId;
        return userId === groceryPreferences.defaultSharedListOwnerId;
      });

      if (defaultSharedList) {
        console.log('Auto-switching to default shared list:', defaultSharedList);
        switchToSharedList(groceryPreferences.defaultSharedListOwnerId);
      }
    }
  }, [groceryPreferences, currentListOwnerId, sharedLists, switchToSharedList]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    // Existing grocery items state
    items,
    isLoading,
    error,
    suggestions,
    isLoadingSuggestions,
    errorSuggestions,
    insights,
    isLoadingInsights,
    errorInsights,

    // Collaboration state
    groceryList,
    isShared,
    collaborators,
    shareSettings,
    userRole,
    sharedLists,
    pendingInvitations,
    isLoadingCollaboration,
    collaborationError,
    currentListOwnerId,

    // Existing grocery methods
    fetchGroceries,
    addGroceryItems,
    updateGroceryItem,
    deleteGroceryItem,
    fetchSuggestions,
    fetchInsights,
    updateCategories,
    triggerRefresh,
    refreshGroceries: triggerRefresh, // Alias for convenience

    // Collaboration methods
    fetchGroceryList,
    enableSharing,
    disableSharing,
    updateShareSettings,
    addCollaborator,
    removeCollaborator,
    fetchSharedLists,
    fetchPendingInvitations,
    acceptInvitation,
    declineInvitation,
    switchToSharedList,
    switchToMyList,
  }), [
    // Existing dependencies
    items,
    isLoading,
    error,
    suggestions,
    isLoadingSuggestions,
    errorSuggestions,
    insights,
    isLoadingInsights,
    errorInsights,

    // Collaboration dependencies
    groceryList,
    isShared,
    collaborators,
    shareSettings,
    userRole,
    sharedLists,
    pendingInvitations,
    isLoadingCollaboration,
    collaborationError,
    currentListOwnerId,

    // Method dependencies
    fetchGroceries,
    addGroceryItems,
    updateGroceryItem,
    deleteGroceryItem,
    fetchSuggestions,
    fetchInsights,
    updateCategories,
    triggerRefresh,
    fetchGroceryList,
    enableSharing,
    disableSharing,
    updateShareSettings,
    addCollaborator,
    removeCollaborator,
    fetchSharedLists,
    fetchPendingInvitations,
    acceptInvitation,
    declineInvitation,
    switchToSharedList,
    switchToMyList,
  ]);

  return (
    <GroceryContext.Provider value={contextValue}>
      {children}
    </GroceryContext.Provider>
  );
};

// Custom hook to use the GroceryContext
export const useGroceryContext = (): GroceryContextType => {
  const context = useContext(GroceryContext);
  if (context === undefined) {
    throw new Error('useGroceryContext must be used within a GroceryProvider');
  }
  return context;
};

// Alias for convenience
export const useGrocery = useGroceryContext;
