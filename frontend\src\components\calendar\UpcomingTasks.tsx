import React from 'react';
import { format, addDays, isWithinInterval, startOfDay, endOfDay } from 'date-fns';
import { CalendarSyncIndicator } from '../tasks/CalendarSyncIndicator';
import { TaskModel } from '../../../lib/types/task.model';

interface UpcomingTasksProps {
  selectedDate: Date;
  filters: {
    categories: string[];
    priority: string;
    status: string;
  };
}

const UpcomingTasks: React.FC<UpcomingTasksProps> = ({
  selectedDate,
  filters,
}) => {
  // Mock tasks - replace with actual task fetching
  const tasks: TaskModel[] = [];

  const getUpcomingTasks = () => {
    const nextWeek = addDays(selectedDate, 7);
    return tasks
      .filter(task => {
        if (!task.deadline) return false;
        const taskDate = new Date(task.deadline);
        return isWithinInterval(taskDate, {
          start: startOfDay(selectedDate),
          end: endOfDay(nextWeek),
        });
      })
      .sort((a, b) => {
        if (!a.deadline || !b.deadline) return 0;
        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
      });
  };

  const upcomingTasks = getUpcomingTasks();

  return (
    <div className="p-4 border-t border-gray-200">
      <h3 className="text-sm font-semibold text-gray-900 mb-4">
        Upcoming Tasks
      </h3>

      {upcomingTasks.length === 0 ? (
        <p className="text-sm text-gray-500">No upcoming tasks</p>
      ) : (
        <div className="space-y-3">
          {upcomingTasks.map(task => (
            <div
              key={task._id}
              className="bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow transition-shadow"
            >
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium text-sm">{task.title}</span>
                <CalendarSyncIndicator
                  syncStatus={task.metadata?.calendarSyncStatus === 'not_synced' ? undefined : task.metadata?.calendarSyncStatus}
                  lastSync={task.metadata?.calendarLastSyncedAt ? new Date(task.metadata.calendarLastSyncedAt) : null}
                />
              </div>

              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  {task.priority && (
                    <span
                      className={`px-1.5 py-0.5 rounded ${
                        task.priority === 'High' || task.priority === 'Critical'
                          ? 'bg-red-100 text-red-800'
                          : task.priority === 'Medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}
                    >
                      {task.priority}
                    </span>
                  )}
                  {task.categories && task.categories.length > 0 && (
                    <span className="text-gray-500">
                      {Array.isArray(task.categories)
                        ? (typeof task.categories[0] === 'string' ? task.categories[0] : task.categories[0]?.name)
                        : task.categories}
                    </span>
                  )}
                </div>
                <span className="text-gray-500">
                  {task.deadline && format(new Date(task.deadline), 'MMM d, h:mm a')}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UpcomingTasks;
