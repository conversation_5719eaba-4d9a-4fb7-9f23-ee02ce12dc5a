import React from 'react';
import {
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  format,
  isToday,
  addHours,
  startOfDay,
} from 'date-fns';
import { CalendarSyncIndicator } from '../../tasks/CalendarSyncIndicator';
import { TaskModel } from '../../../../lib/types/task.model';

interface WeekViewProps {
  selectedDate: Date;
  filters: {
    categories: string[];
    priority: string;
    status: string;
  };
}

const WeekView: React.FC<WeekViewProps> = ({ selectedDate, filters }) => {
  const weekStart = startOfWeek(selectedDate);
  const weekEnd = endOfWeek(selectedDate);
  const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
  const hours = Array.from({ length: 24 }, (_, i) => i);

  // Mock tasks - replace with actual task fetching
  const tasks: TaskModel[] = [];

  const getTasksForDateAndHour = (date: Date, hour: number) => {
    return tasks.filter(task => {
      if (!task.deadline) return false;
      const taskDate = new Date(task.deadline);
      return (
        taskDate.getDate() === date.getDate() &&
        taskDate.getMonth() === date.getMonth() &&
        taskDate.getFullYear() === date.getFullYear() &&
        taskDate.getHours() === hour
      );
    });
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-auto">
      <div className="flex">
        {/* Time column */}
        <div className="w-20 border-r border-gray-200">
          <div className="h-12 border-b border-gray-200" /> {/* Header spacer */}
          {hours.map(hour => (
            <div
              key={hour}
              className="h-12 border-b border-gray-200 text-xs text-gray-500 text-right pr-2 pt-1"
            >
              {format(addHours(startOfDay(selectedDate), hour), 'h a')}
            </div>
          ))}
        </div>

        {/* Days columns */}
        <div className="flex-1 grid grid-cols-7 divide-x divide-gray-200">
          {days.map(day => (
            <div key={day.toString()} className="min-w-[120px]">
              {/* Day header */}
              <div
                className={`h-12 border-b border-gray-200 text-center py-2 ${
                  isToday(day) ? 'bg-blue-50' : ''
                }`}
              >
                <div className="text-sm font-medium text-gray-900">
                  {format(day, 'EEE')}
                </div>
                <div
                  className={`text-sm ${
                    isToday(day) ? 'text-blue-600 font-semibold' : 'text-gray-500'
                  }`}
                >
                  {format(day, 'd')}
                </div>
              </div>

              {/* Hour cells */}
              {hours.map(hour => {
                const tasksForHour = getTasksForDateAndHour(day, hour);
                return (
                  <div
                    key={`${day}-${hour}`}
                    className="h-12 border-b border-gray-200 p-1"
                  >
                    {tasksForHour.map(task => (
                      <div
                        key={task._id}
                        className="text-xs p-1 rounded bg-blue-50 text-blue-700 flex items-center justify-between mb-1"
                      >
                        <span className="truncate">{task.title}</span>
                        <CalendarSyncIndicator
                          syncStatus={task.metadata?.calendarSyncStatus === 'not_synced' ? undefined : task.metadata?.calendarSyncStatus}
                          lastSync={task.metadata?.calendarLastSyncedAt ? new Date(task.metadata.calendarLastSyncedAt) : null}
                        />
                      </div>
                    ))}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WeekView;
