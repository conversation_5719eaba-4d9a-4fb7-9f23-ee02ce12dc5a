import { Types } from 'mongoose';
import { Task, ITask } from '../models/Task';
import { User } from '../models/User';
import { googleCalendarService } from './googleCalendarService';
import secureLogger from '../utils/secureLogger';

class TaskService {
  async createTask(userId: Types.ObjectId, taskData: Partial<ITask>): Promise<ITask> {
    try {
      const task = await Task.create({
        ...taskData,
        userId,
      });

      // Handle calendar sync if task has deadline
      if (task.deadline) {
        await this.syncTaskWithCalendar(task);
      }

      return task;
    } catch (error: any) {
      secureLogger.error('[TaskService] Failed to create task:', error);
      throw error;
    }
  }

  async updateTask(taskId: string, userId: Types.ObjectId, updates: Partial<ITask>): Promise<ITask | null> {
    try {
      const task = await Task.findOne({ _id: taskId, userId });
      if (!task) {
        return null;
      }

      // Update task fields
      Object.assign(task, updates);
      await task.save();

      // Handle calendar sync if deadline was updated
      if (updates.deadline !== undefined) {
        await this.syncTaskWithCalendar(task);
      }

      return task;
    } catch (error: any) {
      secureLogger.error(`[TaskService] Failed to update task ${taskId}:`, error);
      throw error;
    }
  }

  async deleteTask(taskId: string, userId: Types.ObjectId): Promise<boolean> {
    try {
      const task = await Task.findOne({ _id: taskId, userId });
      if (!task) {
        return false;
      }

      // Delete from Google Calendar if synced
      if (task.metadata?.googleCalendarEventId) {
        await this.deleteTaskFromCalendar(task);
      }

      await task.remove();
      return true;
    } catch (error: any) {
      secureLogger.error(`[TaskService] Failed to delete task ${taskId}:`, error);
      throw error;
    }
  }

  private async syncTaskWithCalendar(task: ITask): Promise<void> {
    const user = await User.findById(task.userId);
    if (!user?.googleCalendarSyncEnabled) {
      return;
    }

    try {
      if (task.metadata?.googleCalendarEventId) {
        // Update existing event
        const updated = await googleCalendarService.updateEvent(
          user,
          task,
          task.metadata.googleCalendarEventId
        );

        if (updated) {
          task.metadata.googleCalendarSyncStatus = 'synced';
          task.metadata.googleCalendarLastSync = new Date();
        } else {
          task.metadata.googleCalendarSyncStatus = 'failed';
        }
      } else {
        // Create new event
        const eventId = await googleCalendarService.createEvent(user, task);
        if (eventId) {
          task.metadata = task.metadata || {};
          task.metadata.googleCalendarEventId = eventId;
          task.metadata.googleCalendarSyncStatus = 'synced';
          task.metadata.googleCalendarLastSync = new Date();
        } else {
          task.metadata = task.metadata || {};
          task.metadata.googleCalendarSyncStatus = 'failed';
        }
      }
      await task.save();
    } catch (error: any) {
      secureLogger.error(`[TaskService] Failed to sync task ${task._id} with calendar:`, error);
      task.metadata = task.metadata || {};
      task.metadata.googleCalendarSyncStatus = 'failed';
      await task.save();
    }
  }

  private async deleteTaskFromCalendar(task: ITask): Promise<void> {
    if (!task.metadata?.googleCalendarEventId) {
      return;
    }

    const user = await User.findById(task.userId);
    if (!user?.googleCalendarSyncEnabled) {
      return;
    }

    try {
      await googleCalendarService.deleteEvent(user, task.metadata.googleCalendarEventId);
    } catch (error: any) {
      secureLogger.error(`[TaskService] Failed to delete calendar event for task ${task._id}:`, error);
      // Continue with task deletion even if calendar event deletion fails
    }
  }

  async resyncFailedTasks(): Promise<void> {
    try {
      const failedTasks = await Task.find({
        'metadata.googleCalendarSyncStatus': 'failed',
        deadline: { $exists: true }
      });

      for (const task of failedTasks) {
        await this.syncTaskWithCalendar(task);
      }
    } catch (error: any) {
      secureLogger.error('[TaskService] Failed to resync failed tasks:', error);
    }
  }
}

export const taskService = new TaskService();
