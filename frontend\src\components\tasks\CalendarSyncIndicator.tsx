import React from 'react';
import { Toolt<PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from '../../../components/ui/tooltip';
import { Calendar, Check, X, Loader2 } from 'lucide-react';

interface CalendarSyncIndicatorProps {
  syncStatus?: 'pending' | 'synced' | 'failed';
  lastSync?: Date | null;
}

export const CalendarSyncIndicator: React.FC<CalendarSyncIndicatorProps> = ({
  syncStatus,
  lastSync,
}) => {
  if (!syncStatus) return null;

  const getIcon = () => {
    switch (syncStatus) {
      case 'synced':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <X className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getTooltipText = () => {
    switch (syncStatus) {
      case 'synced':
        return `Synced to Google Calendar${lastSync ? ` on ${new Date(lastSync).toLocaleString()}` : ''}`;
      case 'failed':
        return 'Failed to sync with Google Calendar';
      case 'pending':
        return 'Syncing with Google Calendar...';
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            {getIcon()}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
