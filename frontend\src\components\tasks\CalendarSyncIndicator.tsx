import React from 'react';
import { Too<PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, Tooltip<PERSON>rovider, TooltipTrigger } from '../../../components/ui/tooltip';
import { Calendar, Check, X, Loader2 } from 'lucide-react';

interface CalendarSyncIndicatorProps {
  syncStatus?: 'pending' | 'synced' | 'failed';
  lastSync?: Date | null;
  // Backward compatibility - accept task object
  task?: any;
}

export const CalendarSyncIndicator: React.FC<CalendarSyncIndicatorProps> = ({
  syncStatus,
  lastSync,
  task,
}) => {
  // Backward compatibility: extract from task if provided
  // Backend stores calendar sync at top level, not in metadata
  const actualSyncStatus = syncStatus || (task?.calendarSyncStatus === 'not_synced' ? undefined : task?.calendarSyncStatus);
  const actualLastSync = lastSync || (task?.calendarLastSyncedAt ? new Date(task.calendarLastSyncedAt) : null);

  if (!actualSyncStatus) return null;

  const getIcon = () => {
    switch (actualSyncStatus) {
      case 'synced':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <X className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getTooltipText = () => {
    switch (actualSyncStatus) {
      case 'synced':
        return `Synced to Google Calendar${actualLastSync ? ` on ${actualLastSync.toLocaleString()}` : ''}`;
      case 'failed':
        return 'Failed to sync with Google Calendar';
      case 'pending':
        return 'Syncing with Google Calendar...';
      default:
        return '';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-gray-400" />
            {getIcon()}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
