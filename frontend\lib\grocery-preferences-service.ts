import { api } from './api';

export interface GroceryPreferences {
  defaultListType: 'personal' | 'shared';
  defaultSharedListOwnerId: string | null;
  autoSwitchToDefault: boolean;
  showPersonalListInSidebar: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: {
    message: string;
    code: string;
  };
}

class GroceryPreferencesService {
  private endpoint = '/api/settings';

  /**
   * Get grocery preferences for the current user
   */
  async getGroceryPreferences(): Promise<GroceryPreferences> {
    try {
      const response = await api.get<ApiResponse<GroceryPreferences>>(`${this.endpoint}/grocery-preferences`);

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || 'Failed to fetch grocery preferences');
      }
    } catch (error: any) {
      console.error('Error fetching grocery preferences:', error);
      if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }
      throw new Error('Failed to fetch grocery preferences');
    }
  }

  /**
   * Update grocery preferences for the current user
   */
  async updateGroceryPreferences(preferences: Partial<GroceryPreferences>): Promise<GroceryPreferences> {
    try {
      const response = await api.put<ApiResponse<GroceryPreferences>>(`${this.endpoint}/grocery-preferences`, {
        groceryPreferences: preferences
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || 'Failed to update grocery preferences');
      }
    } catch (error: any) {
      console.error('Error updating grocery preferences:', error);
      if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }
      throw new Error('Failed to update grocery preferences');
    }
  }

  /**
   * Get available shared lists for the dropdown
   */
  async getAvailableSharedLists(): Promise<Array<{ id: string; ownerName: string; ownerEmail: string }>> {
    try {
      // This would use the existing grocery service to get shared lists
      const { groceryService } = await import('./grocery-service');
      const sharedLists = await groceryService.getSharedLists();

      return sharedLists.map(list => {
        const userId = typeof list.userId === 'string' ? list.userId : list.userId._id;
        const ownerName = typeof list.userId === 'string' ? 'User' : list.userId.name;
        const ownerEmail = typeof list.userId === 'string' ? '' : list.userId.email;

        return {
          id: userId,
          ownerName: ownerName,
          ownerEmail: ownerEmail
        };
      });
    } catch (error: any) {
      console.error('Error fetching available shared lists:', error);
      return [];
    }
  }

  /**
   * Validate if a shared list is still accessible
   */
  async validateSharedListAccess(listOwnerId: string): Promise<boolean> {
    try {
      const availableLists = await this.getAvailableSharedLists();
      return availableLists.some(list => list.id === listOwnerId);
    } catch (error) {
      console.error('Error validating shared list access:', error);
      return false;
    }
  }
}

export const groceryPreferencesService = new GroceryPreferencesService();
