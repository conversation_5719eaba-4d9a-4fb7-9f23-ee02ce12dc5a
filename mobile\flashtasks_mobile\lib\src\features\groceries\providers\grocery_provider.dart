import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/grocery_service.dart';
import '../models/grocery_item.dart';
import '../models/grocery_insights.dart';
import '../models/grocery_list.dart';
import '../models/grocery_invitation.dart';
import '../../settings/providers/grocery_preferences_provider.dart';

/// State class for grocery provider
class GroceryState {
  final List<GroceryItem> items;
  final bool isLoading;
  final String? error;

  final List<String> suggestions;
  final bool isLoadingSuggestions;
  final String? errorSuggestions;

  final GroceryInsights? insights;
  final bool isLoadingInsights;
  final String? errorInsights;

  // NEW: Collaboration fields
  final GroceryList? groceryList;
  final bool isLoadingList;
  final String? errorList;

  final List<GroceryList> sharedLists;
  final bool isLoadingSharedLists;
  final String? errorSharedLists;

  final List<GroceryListInvitation> pendingInvitations;
  final bool isLoadingInvitations;
  final String? errorInvitations;

  final String? currentListOwnerId; // Track which list we're viewing

  GroceryState({
    this.items = const [],
    this.isLoading = false,
    this.error,
    this.suggestions = const [],
    this.isLoadingSuggestions = false,
    this.errorSuggestions,
    this.insights,
    this.isLoadingInsights = false,
    this.errorInsights,
    // NEW: Collaboration fields
    this.groceryList,
    this.isLoadingList = false,
    this.errorList,
    this.sharedLists = const [],
    this.isLoadingSharedLists = false,
    this.errorSharedLists,
    this.pendingInvitations = const [],
    this.isLoadingInvitations = false,
    this.errorInvitations,
    this.currentListOwnerId,
  });

  GroceryState copyWith({
    List<GroceryItem>? items,
    bool? isLoading,
    String? error,
    List<String>? suggestions,
    bool? isLoadingSuggestions,
    String? errorSuggestions,
    GroceryInsights? insights,
    bool? isLoadingInsights,
    String? errorInsights,
    // NEW: Collaboration fields
    GroceryList? groceryList,
    bool? isLoadingList,
    String? errorList,
    List<GroceryList>? sharedLists,
    bool? isLoadingSharedLists,
    String? errorSharedLists,
    List<GroceryListInvitation>? pendingInvitations,
    bool? isLoadingInvitations,
    String? errorInvitations,
    String? currentListOwnerId,
  }) {
    return GroceryState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      suggestions: suggestions ?? this.suggestions,
      isLoadingSuggestions: isLoadingSuggestions ?? this.isLoadingSuggestions,
      errorSuggestions: errorSuggestions,
      insights: insights ?? this.insights,
      isLoadingInsights: isLoadingInsights ?? this.isLoadingInsights,
      errorInsights: errorInsights,
      // NEW: Collaboration fields
      groceryList: groceryList ?? this.groceryList,
      isLoadingList: isLoadingList ?? this.isLoadingList,
      errorList: errorList,
      sharedLists: sharedLists ?? this.sharedLists,
      isLoadingSharedLists: isLoadingSharedLists ?? this.isLoadingSharedLists,
      errorSharedLists: errorSharedLists,
      pendingInvitations: pendingInvitations ?? this.pendingInvitations,
      isLoadingInvitations: isLoadingInvitations ?? this.isLoadingInvitations,
      errorInvitations: errorInvitations,
      currentListOwnerId: currentListOwnerId ?? this.currentListOwnerId,
    );
  }
}

/// Notifier class for grocery state management
class GroceryNotifier extends StateNotifier<GroceryState> {
  final GroceryService _groceryService;
  final Ref _ref;

  GroceryNotifier({required GroceryService groceryService, required Ref ref})
      : _groceryService = groceryService,
        _ref = ref,
        super(GroceryState()) {
    // Initialize data on creation
    fetchGroceries();
    fetchInsights();
    fetchGroceryList();
    fetchSharedLists();
    fetchPendingInvitations();

    // Set up auto-switch listener
    _setupAutoSwitchListener();
  }

  /// Set up listener for auto-switching to default list
  void _setupAutoSwitchListener() {
    // Listen to grocery preferences changes
    _ref.listen<GroceryPreferencesState>(groceryPreferencesProvider, (previous, next) {
      _handleAutoSwitch(next);
    });

    // Also check current preferences
    final currentPreferences = _ref.read(groceryPreferencesProvider);
    _handleAutoSwitch(currentPreferences);
  }

  /// Handle auto-switch logic based on preferences
  void _handleAutoSwitch(GroceryPreferencesState preferencesState) {
    final preferences = preferencesState.preferences;

    if (preferences?.autoSwitchToDefault == true &&
        preferences?.defaultListType == 'shared' &&
        preferences?.defaultSharedListOwnerId != null &&
        state.currentListOwnerId == null && // Only auto-switch if not already on a specific list
        state.sharedLists.isNotEmpty) {

      // Check if the default shared list is available
      final defaultSharedList = state.sharedLists.where((list) =>
        list.userId == preferences!.defaultSharedListOwnerId
      ).firstOrNull;

      if (defaultSharedList != null) {
        // Auto-switch to default shared list
        switchToSharedList(preferences!.defaultSharedListOwnerId!);
      }
    }
  }

  /// Fetch all grocery items
  /// [listOwnerId] - Optional parameter to fetch items from a shared list
  Future<void> fetchGroceries({String? listOwnerId}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final items = await _groceryService.getGroceryItems(listOwnerId: listOwnerId);
      state = state.copyWith(
        items: items,
        isLoading: false,
        currentListOwnerId: listOwnerId,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Fetch grocery suggestions
  Future<void> fetchSuggestions({int limit = 5}) async {
    state = state.copyWith(isLoadingSuggestions: true, errorSuggestions: null);

    try {
      final suggestions = await _groceryService.getGrocerySuggestions(limit: limit);

      // Filter out suggestions that are already in the unchecked items list
      final currentUncheckedItemNames = state.items
          .where((item) => !item.isChecked)
          .map((item) => item.name.toLowerCase())
          .toList();

      final filteredSuggestions = suggestions
          .where((suggestion) => !currentUncheckedItemNames.contains(suggestion.toLowerCase()))
          .toList();

      state = state.copyWith(
        suggestions: filteredSuggestions,
        isLoadingSuggestions: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingSuggestions: false,
        errorSuggestions: e.toString(),
      );
    }
  }

  /// Fetch grocery insights
  Future<void> fetchInsights() async {
    state = state.copyWith(isLoadingInsights: true, errorInsights: null);

    try {
      final insights = await _groceryService.getGroceryInsights();
      state = state.copyWith(
        insights: insights,
        isLoadingInsights: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingInsights: false,
        errorInsights: e.toString(),
      );
    }
  }

  /// Add a new grocery item
  /// Uses current list context or user's default preferences
  Future<void> addItem(String name, {String? quantity, String? categoryName, String? targetListOwnerId}) async {
    if (name.trim().isEmpty) return;

    try {
      // Determine target list: explicit parameter > current context > user preferences
      String? listOwnerId = targetListOwnerId ?? state.currentListOwnerId;

      // Prepare item data
      final itemData = <String, dynamic>{
        'name': name.trim(),
      };

      if (quantity != null && quantity.isNotEmpty) {
        itemData['quantity'] = quantity;
      }

      if (categoryName != null && categoryName.isNotEmpty) {
        itemData['category'] = {'name': categoryName};
      }

      // Optimistic update (add temporary item to the list)
      final tempId = 'temp-${DateTime.now().millisecondsSinceEpoch}';
      final tempItem = GroceryItem(
        id: tempId,
        name: name.trim(),
        isChecked: false,
        category: categoryName != null ? GroceryCategoryInfo(name: categoryName) : null,
        quantity: quantity,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      state = state.copyWith(
        items: [...state.items, tempItem],
      );

      // Make API call
      final addedItems = await _groceryService.addGroceryItems([itemData], listOwnerId: listOwnerId);

      // Update state with actual items from API
      final updatedItems = state.items
          .where((item) => item.id != tempId)
          .toList()
        ..addAll(addedItems);

      state = state.copyWith(items: updatedItems);

      // Refresh suggestions after adding an item
      fetchSuggestions();
    } catch (e) {
      // Revert optimistic update on error
      final updatedItems = state.items
          .where((item) => !item.id.startsWith('temp-'))
          .toList();

      state = state.copyWith(
        items: updatedItems,
        error: e.toString(),
      );
    }
  }

  /// Add item using user's default preferences
  /// This method respects the user's grocery preferences for default list selection
  Future<void> addItemWithPreferences(String name, {String? quantity, String? categoryName}) async {
    // For now, use the current context. In a full implementation, this would
    // check user preferences to determine the target list
    await addItem(name, quantity: quantity, categoryName: categoryName);
  }

  /// Toggle the checked status of a grocery item
  Future<void> toggleCheck(String id) async {
    try {
      // Find the item
      final item = state.items.firstWhere((item) => item.id == id);

      // Optimistic update
      final updatedItems = state.items.map((i) {
        if (i.id == id) {
          return i.copyWith(isChecked: !i.isChecked);
        }
        return i;
      }).toList();

      state = state.copyWith(items: updatedItems);

      // Make API call
      await _groceryService.updateGroceryItem(id, {'isChecked': !item.isChecked});

      // Refresh suggestions if needed (when checking off an item)
      if (!item.isChecked) {
        fetchSuggestions();
        fetchInsights(); // Also refresh insights when checking items
      }
    } catch (e) {
      // Revert optimistic update on error
      await fetchGroceries();
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete a grocery item
  Future<void> deleteItem(String id) async {
    try {
      // Optimistic update
      final updatedItems = state.items.where((item) => item.id != id).toList();

      state = state.copyWith(items: updatedItems);

      // Make API call
      await _groceryService.deleteGroceryItem(id);

      // Refresh suggestions after deleting an item
      fetchSuggestions();
    } catch (e) {
      // Revert optimistic update on error
      await fetchGroceries();
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete all checked grocery items
  Future<void> deleteCheckedItems() async {
    try {
      // Optimistic update
      final updatedItems = state.items.where((item) => !item.isChecked).toList();

      state = state.copyWith(items: updatedItems);

      // Make API call
      await _groceryService.deleteCheckedGroceryItems();

      // Refresh insights after bulk delete
      fetchInsights();
    } catch (e) {
      // Revert optimistic update on error
      await fetchGroceries();
      state = state.copyWith(error: e.toString());
    }
  }

  /// Update a grocery item
  Future<void> updateItem(String id, Map<String, dynamic> updates) async {
    try {
      // Find the item
      final item = state.items.firstWhere((item) => item.id == id);

      // Prepare optimistic update
      final updatedItem = item.copyWith(
        name: updates['name'] ?? item.name,
        quantity: updates['quantity'] ?? item.quantity,
        notes: updates['notes'] ?? item.notes,
        category: updates['category'] != null
            ? (updates['category'] is String
                // Handle when category is just a string (API v2)
                ? GroceryCategoryInfo(name: updates['category'])
                // Handle when category is an object (backward compatibility)
                : GroceryCategoryInfo(
                    name: updates['category']['name'],
                    color: updates['category']['color'],
                  ))
            : item.category,
      );

      // Apply optimistic update
      final updatedItems = state.items.map((i) {
        if (i.id == id) {
          return updatedItem;
        }
        return i;
      }).toList();

      state = state.copyWith(items: updatedItems);

      // Make API call
      await _groceryService.updateGroceryItem(id, updates);
    } catch (e) {
      // Revert optimistic update on error
      await fetchGroceries();
      state = state.copyWith(error: e.toString());
    }
  }

  /// Update categories for uncategorized items
  Future<Map<String, int>> updateCategories() async {
    try {
      final result = await _groceryService.updateCategories();

      // Refresh grocery list to show updated categories
      await fetchGroceries(listOwnerId: state.currentListOwnerId);

      return result;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  // ===== COLLABORATION METHODS =====

  /// Fetch grocery list metadata
  Future<void> fetchGroceryList() async {
    state = state.copyWith(isLoadingList: true, errorList: null);

    try {
      final groceryList = await _groceryService.getGroceryList();
      state = state.copyWith(
        groceryList: groceryList,
        isLoadingList: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingList: false,
        errorList: e.toString(),
      );
    }
  }

  /// Fetch shared grocery lists
  Future<void> fetchSharedLists() async {
    state = state.copyWith(isLoadingSharedLists: true, errorSharedLists: null);

    try {
      final sharedLists = await _groceryService.getSharedLists();
      state = state.copyWith(
        sharedLists: sharedLists,
        isLoadingSharedLists: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingSharedLists: false,
        errorSharedLists: e.toString(),
      );
    }
  }

  /// Fetch pending invitations
  Future<void> fetchPendingInvitations() async {
    state = state.copyWith(isLoadingInvitations: true, errorInvitations: null);

    try {
      final invitations = await _groceryService.getPendingInvitations();
      state = state.copyWith(
        pendingInvitations: invitations,
        isLoadingInvitations: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingInvitations: false,
        errorInvitations: e.toString(),
      );
    }
  }

  /// Switch to viewing a shared list
  Future<void> switchToSharedList(String listOwnerId) async {
    await fetchGroceries(listOwnerId: listOwnerId);
  }

  /// Switch back to personal list
  Future<void> switchToPersonalList() async {
    await fetchGroceries(listOwnerId: null);
  }

  /// Enable sharing for grocery list
  Future<void> enableSharing(GroceryShareSettings shareSettings) async {
    try {
      final updatedList = await _groceryService.enableSharing(shareSettings);
      state = state.copyWith(groceryList: updatedList);
    } catch (e) {
      state = state.copyWith(errorList: e.toString());
      rethrow;
    }
  }

  /// Disable sharing for grocery list
  Future<void> disableSharing() async {
    try {
      final updatedList = await _groceryService.disableSharing();
      state = state.copyWith(groceryList: updatedList);
    } catch (e) {
      state = state.copyWith(errorList: e.toString());
      rethrow;
    }
  }

  /// Update share settings
  Future<void> updateShareSettings(GroceryShareSettings shareSettings) async {
    try {
      final updatedList = await _groceryService.updateShareSettings(shareSettings);
      state = state.copyWith(groceryList: updatedList);
    } catch (e) {
      state = state.copyWith(errorList: e.toString());
      rethrow;
    }
  }

  /// Add collaborator to grocery list
  Future<void> addCollaborator(String email, CollaboratorRole role) async {
    try {
      final updatedList = await _groceryService.addCollaborator(email, role);
      state = state.copyWith(groceryList: updatedList);
    } catch (e) {
      state = state.copyWith(errorList: e.toString());
      rethrow;
    }
  }

  /// Remove collaborator from grocery list
  Future<void> removeCollaborator(String collaboratorId) async {
    try {
      final updatedList = await _groceryService.removeCollaborator(collaboratorId);
      state = state.copyWith(groceryList: updatedList);
    } catch (e) {
      state = state.copyWith(errorList: e.toString());
      rethrow;
    }
  }

  /// Accept an invitation
  Future<void> acceptInvitation(String token) async {
    try {
      await _groceryService.acceptInvitation(token);
      // Refresh data after accepting invitation
      await fetchPendingInvitations();
      await fetchSharedLists();
    } catch (e) {
      state = state.copyWith(errorInvitations: e.toString());
      rethrow;
    }
  }

  /// Decline an invitation
  Future<void> declineInvitation(String token) async {
    try {
      await _groceryService.declineInvitation(token);
      // Refresh invitations after declining
      await fetchPendingInvitations();
    } catch (e) {
      state = state.copyWith(errorInvitations: e.toString());
      rethrow;
    }
  }
}

/// Provider for grocery state
final groceryProvider = StateNotifierProvider<GroceryNotifier, GroceryState>((ref) {
  final groceryService = ref.watch(groceryServiceProvider);
  return GroceryNotifier(groceryService: groceryService, ref: ref);
});
