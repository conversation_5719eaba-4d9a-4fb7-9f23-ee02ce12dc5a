import { useState, useEffect, useRef } from 'react';
import { SyncResult } from '../lib/calendar-sync-service';

interface UseSyncStatusPollingProps {
  taskId?: string;
  initialStatus?: string;
  pollInterval?: number;
  enabled?: boolean;
}

export function useSyncStatusPolling({
  taskId,
  initialStatus,
  pollInterval = 30000, // 30 seconds
  enabled = true,
}: UseSyncStatusPollingProps) {
  const [syncStatus, setSyncStatus] = useState<SyncResult>({
    success: false,
    status: (initialStatus as SyncResult['status']) || 'not_synced',
  });
  const [isPolling, setIsPolling] = useState(false);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  const stopPolling = () => {
    if (pollingRef.current) {
      clearTimeout(pollingRef.current);
      pollingRef.current = null;
      setIsPolling(false);
    }
  };

  const checkStatus = async () => {
    if (!taskId || !enabled) return;

    try {
      // In a real implementation, this would call an API endpoint
      // const result = await calendarSyncService.getTaskSyncStatus(taskId);
      // setSyncStatus(result);

      // For now, we'll simulate a successful sync after 2 seconds
      if (syncStatus.status !== 'synced') {
        setTimeout(() => {
          setSyncStatus({
            success: true,
            status: 'synced',
            eventId: `event-${Date.now()}`,
          });
          stopPolling();
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking sync status:', error);
      setSyncStatus({
        success: false,
        status: 'failed',
        error: 'Failed to check sync status',
      });
      stopPolling();
    }
  };

  // Start or stop polling based on enabled state and taskId
  useEffect(() => {
    if (taskId && enabled && !isPolling) {
      setIsPolling(true);
      pollingRef.current = setInterval(checkStatus, pollInterval);
      // Initial check
      checkStatus();
    }

    return () => {
      stopPolling();
    };
  }, [taskId, enabled]);

  // Update status when initialStatus changes
  useEffect(() => {
    if (initialStatus) {
      setSyncStatus(prev => ({
        ...prev,
        status: initialStatus as SyncResult['status'],
      }));
    }
  }, [initialStatus]);

  return {
    syncStatus,
    isPolling,
    checkStatus,
    stopPolling,
  };
}
