// Import the environment utilities
import { API_URL } from './lib/environment';

// API Configuration
// In development: Points to local backend (http://localhost:3001)
// In production: Points to production backend (https://flashtasks-ai.onrender.com)
export const API_BASE_URL = API_URL;

// Other configuration constants can be added here
export const APP_NAME = 'FlashTasks AI'

// API endpoint structure - this ensures consistent endpoint usage across services
// Note: The /api prefix is now added by the api.ts baseURL, so we don't include it here
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: '/auth',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  VALIDATE_TOKEN: '/auth/validate-token',
  REFRESH_TOKEN: '/auth/refresh',
  LOGOUT: '/auth/logout',
  CSRF_TOKEN: '/csrf-token',

  // Task endpoints
  TASKS: '/tasks',
  TASK_TOGGLE: (id: string) => `/tasks/${id}/toggle`,
  TASKS_BY_LOCATION: (locationId: string) => `/tasks/location/${locationId}`,

  // Category endpoints
  CATEGORIES: '/categories',

  // AI endpoints
  AI: '/ai',
  AI_QUICK_ADD: '/ai/quick-add',
  AI_ASK: '/ai/ask',
  AI_INSIGHTS: '/ai/insights',
  AI_GENERATE_INSIGHTS: '/ai/generate-insights',

  // Grocery endpoints
  GROCERIES: '/groceries',

  // Location endpoints
  LOCATIONS: '/locations',

  // User endpoints
  USERS: '/users',
  USERS_PROFILE: '/users/profile',
  USERS_CHANGE_PASSWORD: '/users/change-password',
  USERS_DELETE_ACCOUNT: '/users/account',

  // User settings endpoints
  USER_SETTINGS: '/settings',
  USER_SETTINGS_ALL: '/settings/all',
  USER_SETTINGS_PREFERENCES: '/settings/preferences',
  USER_SETTINGS_TASK_SETTINGS: '/settings/task-settings',
  USER_SETTINGS_NOTIFICATION_SETTINGS: '/settings/notification-settings',
  USER_SETTINGS_DATETIME_SETTINGS: '/settings/datetime-settings',
  USER_SETTINGS_KEYTAG_MAPPINGS: '/settings/keytag-mappings',
  USER_SETTINGS_CATEGORY_LOCATION_MAPPINGS: '/settings/category-location-mappings',
  USER_SETTINGS_LOCATION_SUGGESTION_SETTINGS: '/settings/location-suggestion-settings',

  // Notifications endpoints
  NOTIFICATIONS: '/notifications',
  NOTIFICATIONS_PUSH_TOKEN: '/notifications/push-token',

  // Calendar endpoints
  CALENDAR: '/calendar',
}